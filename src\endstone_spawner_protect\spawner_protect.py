from endstone.plugin import Plugin
from endstone.event import event_handler, PlayerInteract<PERSON>vent
from endstone.command import Command, CommandSender


class SpawnerProtect(Plugin):
    """
    Plugin to prevent non-operators from using spawn eggs on spawners.
    Only operators can change spawner types.
    """

    api_version = "0.10"

    def on_enable(self) -> None:
        """Called when the plugin is enabled."""
        self.logger.info("SpawnerProtect has been enabled!")
        self.logger.info("Non-ops cannot use spawn eggs on spawners")

    def on_disable(self) -> None:
        """Called when the plugin is disabled."""
        self.logger.info("SpawnerProtect has been disabled!")

    @event_handler
    def on_player_interact(self, event: PlayerInteractEvent) -> None:
        """
        Handle player interaction events to prevent non-ops from using
        spawn eggs on spawners.

        Note: In API 0.10+, PlayerInteractEvent triggers on both right-clicks
        and left-clicks with air and blocks.
        """
        player = event.player

        # Check if this interaction involves a block
        if not event.has_block():
            return

        block = event.block
        if not block:
            return

        # Check if the block is a spawner (mob_spawner)
        if block.type != "minecraft:mob_spawner":
            return

        # Check if the player has an item in hand
        if not event.has_item():
            return

        item = event.item
        if not item:
            return

        # Check if the item is a spawn egg (spawn eggs end with "_spawn_egg")
        item_type = item.type
        if not item_type.endswith("_spawn_egg"):
            return

        # If player is an operator, allow the action
        if player.is_op:
            return

        # Cancel the event for non-operators
        event.is_cancelled = True

        # Send a message to the player
        player.send_error_message(
            "§cYou don't have permission to change spawner types!"
        )
        player.send_message(
            "§eOnly operators can use spawn eggs on spawners."
        )

        self.logger.info(
            f"{player.name} tried to use {item_type} on a spawner but was blocked"
        )

    def on_command(self, sender: CommandSender, command: Command, args: list[str]) -> bool:
        """
        Handle plugin commands.
        """
        if command.name == "spawnerprotect":
            sender.send_message("§a=== SpawnerProtect ===")
            sender.send_message("§7Version: 1.0.0")
            sender.send_message("§7API Version: 0.10")
            sender.send_message("§7Status: §aActive")
            sender.send_message("§7Protection: Non-ops cannot change spawners")
            return True
        return False