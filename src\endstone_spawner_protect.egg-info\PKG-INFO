Metadata-Version: 2.4
Name: endstone-spawner-protect
Version: 1.0.4
Summary: Prevents non-operators from using spawn eggs on spawners
Author-email: Your Name <<EMAIL>>
License: MIT
Keywords: endstone,plugin,minecraft,bedrock,spawner,protection
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: endstone>=0.10.0
