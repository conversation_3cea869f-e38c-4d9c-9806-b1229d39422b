[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "endstone-spawner-protect"
version = "1.0.4"
description = "Prevents non-operators from using spawn eggs on spawners"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "endstone>=0.10.0"
]
license = {text = "MIT"}
keywords = ["endstone", "plugin", "minecraft", "bedrock", "spawner", "protection"]

classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

[project.entry-points."endstone"]
spawner-protect = "endstone_spawner_protect.spawner_protect:SpawnerProtect"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
spawner_protect = ["plugin.toml"]